import React, { useState, useEffect } from "react";
import { Layout } from "@/components/Layout";
import { SEO } from "@/components/SEO";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, X, Briefcase, Calendar, Wrench, Shield, Clock, Zap, TrendingUp, BadgeDollarSign, Users, Award, ChevronDown, PlusCircle, Percent, Bell, BarChart, Repeat, CreditCard, HelpCircle, Loader2 } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import ProviderSignupDialog from "@/components/ProviderSignupDialog";
import PricingTier from "@/components/PricingTier";
import { planService } from "@/services/planService";
import { ProviderPlan } from "@/components/admin/provider-tier/schemas";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { useStripeCheckout } from "@/hooks/useStripeCheckout";

const ForProviders = () => {
  const [plans, setPlans] = useState<ProviderPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'monthly' | 'yearly'>('monthly');

  // Get authentication state
  const { isAuthenticated, token, user } = useAuth();

  // Stripe checkout hook
  const { redirectToCheckout, isLoading: isCheckoutLoading } = useStripeCheckout();

  // Helper function for general buttons (when no specific plan is available)
  const getGeneralButtonConfig = () => {
    if (!isAuthenticated || !token) {
      // Find the first plan with Stripe integration for "Get Started"
      const stripeEnabledPlan = plans.find(plan => plan.stripe_price_id && plan.stripe_product_id);

      if (stripeEnabledPlan) {
        return {
          text: 'Get Started',
          destination: null, // Will use onClick handler
          isStripe: true,
          plan: stripeEnabledPlan
        };
      }

      return {
        text: 'Get Started',
        destination: '/auth',
        isStripe: false
      };
    }

    const currentPlan = getCurrentUserPlan();
    const currentLevel = getPlanLevel(currentPlan);

    if (currentLevel >= 3) {
      return {
        text: 'Manage Plan',
        destination: '/provider/plans',
        isStripe: false
      };
    }

    return {
      text: 'Upgrade',
      destination: '/provider/plans',
      isStripe: false
    };
  };

  // Helper function to get current user plan
  const getCurrentUserPlan = () => {
    if (!user?.current_subscription) {
      return 'starter'; // Default to starter if no subscription
    }
    return user.current_subscription.plan.name.toLowerCase();
  };

  // Helper function to get plan hierarchy for comparison
  const getPlanLevel = (planName: string) => {
    const plan = planName.toLowerCase();
    switch (plan) {
      case 'starter':
      case 'free':
        return 1;
      case 'pro':
      case 'professional':
        return 2;
      case 'elite':
      case 'enterprise':
        return 3;
      default:
        return 1;
    }
  };

  // Helper function to determine button text, destination, and disabled state
  const getButtonConfig = (plan: ProviderPlan) => {
    const hasStripeIntegration = plan.stripe_price_id && plan.stripe_product_id;

    if (!isAuthenticated || !token) {
      return {
        text: 'Get Started',
        destination: hasStripeIntegration ? null : '/auth', // null means handle with onClick
        disabled: false,
        isStripe: hasStripeIntegration,
        plan: plan
      };
    }

    // User has token, show upgrade/downgrade options
    const currentPlan = getCurrentUserPlan();

    if (plan.name) {
      // For specific plan buttons
      const targetPlan = plan.name.toLowerCase();
      const currentLevel = getPlanLevel(currentPlan);
      const targetLevel = getPlanLevel(targetPlan);

      if (currentPlan === targetPlan) {
        return {
          text: 'Current Plan',
          destination: '/provider/plans',
          disabled: true,
          isStripe: false,
          plan: plan
        };
      }

      if (targetLevel > currentLevel) {
        return {
          text: `Upgrade to ${plan.name}`,
          destination: hasStripeIntegration ? null : '/provider/plans',
          disabled: false,
          isStripe: hasStripeIntegration,
          plan: plan
        };
      } else {
        return {
          text: `Downgrade to ${plan.name}`,
          destination: '/provider/plans',
          disabled: false,
          isStripe: false,
          plan: plan
        };
      }
    }

    // For general buttons - check if user is on highest plan
    const currentLevel = getPlanLevel(currentPlan);
    if (currentLevel >= 3) { // Elite/Enterprise is highest
      return {
        text: 'Manage Plan',
        destination: '/provider/plans',
        disabled: false,
        isStripe: false,
        plan: plan
      };
    }

    return {
      text: 'Upgrade',
      destination: hasStripeIntegration ? null : '/provider/plans',
      disabled: false,
      isStripe: hasStripeIntegration,
      plan: plan
    };
  };

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        // Pass token to get plans with Stripe integration data
        const response = await planService.getPlans(1, 10, token || undefined);

        if (response.success && response.data) {
          setPlans(response.data.data);
        } else {
          setError('Failed to load subscription plans');
        }
      } catch (err) {
        setError('Error fetching plans');
        console.error('Error fetching plans:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, [token]); // Add token as dependency

  // Filter plans based on duration
  const filteredPlans = plans.filter(plan => {
    if (!plan.duration) return activeTab === 'monthly'; // Default to monthly if no duration
    return plan.duration.toLowerCase() === activeTab;
  });

  // Handle Stripe checkout for plans
  const handleStripeCheckout = async (plan: ProviderPlan) => {
    if (!plan.stripe_price_id || !plan.stripe_product_id || !plan.id) {
      console.error('Plan missing required IDs (id, stripe_price_id, stripe_product_id)');
      return;
    }

    if (!isAuthenticated) {
      // Store plan details in URL params and redirect to signup
      const params = new URLSearchParams();
      params.set('redirectToStripe', 'true');
      params.set('planId', plan.id);
      params.set('priceId', plan.stripe_price_id);
      params.set('productId', plan.stripe_product_id);
      params.set('planName', plan.name);

      window.location.href = `/auth?${params.toString()}`;
      return;
    }

    // User is authenticated, proceed with checkout
    await redirectToCheckout(plan.id, plan.stripe_price_id, plan.stripe_product_id);
  };

  const transformPlanToTierProps = (plan: ProviderPlan, index: number) => {
    const buttonConfig = getButtonConfig(plan);
    const isYearly = activeTab === 'yearly';
    const showSavingsBadge = isYearly && plan.yearlyPrice && plan.yearlyPrice < plan.price;

    return {
      title: plan.name,
      price: plan.price === 0 ? 'Free' : String(plan.price),
      yearlyPrice: plan.yearlyPrice ? String(plan.yearlyPrice) : undefined,
      description: plan.description,
      jobMatches: plan.jobMatches,
      transactionFee: plan.transactionFee,
      features: plan.features?.map(feature => ({
        included: feature.included,
        text: feature.text,
        icon: ""
      })) || [],
      ctaText: buttonConfig.text,
      ctaDestination: buttonConfig.destination || undefined,
      ctaDisabled: buttonConfig.disabled || isCheckoutLoading,
      ctaOnClick: buttonConfig.isStripe ? () => handleStripeCheckout(plan) : undefined,
      popular: index === 1, // Make the second plan popular
      highlighted: index === 1,
      isYearly: isYearly,
      showSavingsBadge: showSavingsBadge,
    };
  };

  return <Layout>
    <SEO
        title="Grow Your Service Business – Join as a Provider on JobON"
        description="Join JobON to find jobs, bid on local projects, and get paid faster. Access free tools, smart scheduling, and instant booking. Free to start, scale as you grow."
        localBusinessSchema={true}
        serviceType="For providers"
        serviceSlug="for-providers"
        canonicalUrl="/for-providers"
    />
    <ProviderSignupDialog />
    
    <section className="bg-background pt-16 md:pt-24 lg:pt-32 pb-20">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="flex flex-col lg:flex-row gap-16 items-center space-y-12 lg:space-y-0">
          <div className="w-full lg:w-1/2 space-y-10">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight">
              Grow Your Service Business with <span className="text-primary">JobON</span>
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground leading-relaxed">
              Connect with customers, win more jobs, and streamline your workflow with our all-in-one platform for service providers.
            </p>
            <div className="flex flex-col sm:flex-row gap-5">
              {(() => {
                const buttonConfig = getGeneralButtonConfig();
                if (buttonConfig.isStripe && buttonConfig.plan) {
                  return (
                    <Button
                      size="lg"
                      className="px-8 py-7 text-lg"
                      onClick={() => handleStripeCheckout(buttonConfig.plan!)}
                      disabled={isCheckoutLoading}
                    >
                      {buttonConfig.text}
                    </Button>
                  );
                }
                return (
                  <Button size="lg" className="px-8 py-7 text-lg" asChild>
                    <Link to={buttonConfig.destination!}>{buttonConfig.text}</Link>
                  </Button>
                );
              })()}
              <Button variant="outline" size="lg" className="px-8 py-7 text-lg" asChild>
                <Link to="/how-it-works">Learn More</Link>
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-y-6 gap-x-10">
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Find local jobs</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Smart scheduling</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Business tools</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Fast payouts</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Client management</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Growth analytics</span>
              </div>
            </div>
          </div>
          <div className="w-full lg:w-1/2">
            <div className="relative">
              <div className="absolute -top-10 -left-10 w-48 h-48 bg-primary/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-16 -right-16 w-64 h-64 bg-blue-400/10 rounded-full blur-3xl"></div>
              <img 
                src="/lovable-uploads/6aa5c008-ec77-4565-9424-5812e712eeb8.png" 
                alt="Service provider using JobON mobile app" 
                className="rounded-2xl shadow-2xl border border-border relative z-10 w-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section className="py-16 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-y border-border">
      <div className="container px-4 mx-auto max-w-5xl">
        <div className="flex flex-col md:flex-row items-center justify-between gap-8 text-center md:text-left">
          <div className="md:max-w-2xl space-y-4">
            <h2 className="text-3xl font-semibold">Just getting started? Use JobON free and only pay when you win.</h2>
            <p className="text-xl text-muted-foreground">
              Upgrade to Pro or Elite to keep more of your earnings and unlock premium tools.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            {(() => {
              const buttonConfig = getGeneralButtonConfig();
              if (buttonConfig.isStripe && buttonConfig.plan) {
                return (
                  <Button
                    size="lg"
                    variant="success"
                    className="py-6 px-8 text-lg whitespace-nowrap"
                    onClick={() => handleStripeCheckout(buttonConfig.plan!)}
                    disabled={isCheckoutLoading}
                  >
                    {buttonConfig.text}
                  </Button>
                );
              }
              return (
                <Button size="lg" variant="success" className="py-6 px-8 text-lg whitespace-nowrap" asChild>
                  <Link to={buttonConfig.destination!}>{buttonConfig.text}</Link>
                </Button>
              );
            })()}
          </div>
        </div>
      </div>
    </section>
    
    <section id="pricing-section" className="py-24 md:py-32 bg-muted/30">
      <div className="container px-4 mx-auto max-w-7xl space-y-16">
        <div className="text-center max-w-3xl mx-auto space-y-6">
          <h2 className="text-4xl md:text-5xl font-bold">Simple, transparent pricing</h2>
          <p className="text-xl text-muted-foreground">
            Choose the plan that works for your business needs. Scale as you grow.
          </p>
        </div>
        
        <div className="flex justify-center mb-12">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'monthly' | 'yearly')} className="w-auto">
            <TabsList className="grid w-full grid-cols-2 bg-muted p-1 rounded-lg">
              <TabsTrigger value="monthly" className="px-6 py-2 rounded-md font-medium">
                Monthly
              </TabsTrigger>
              <TabsTrigger value="yearly" className="px-6 py-2 rounded-md font-medium">
                Yearly
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-lg">Loading subscription plans...</span>
          </div>
        ) : error ? (
          <div className="text-center py-20">
            <p className="text-red-500 text-lg mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </div>
        ) : (
          <div className={`grid gap-8 ${filteredPlans.length === 1 ? 'md:grid-cols-1 max-w-md mx-auto' : filteredPlans.length === 2 ? 'md:grid-cols-2 max-w-4xl mx-auto' : 'md:grid-cols-3'}`}>
            {filteredPlans.map((plan, index) => {
              const tierProps = transformPlanToTierProps(plan, index);
              return (
                <PricingTier 
                  key={plan.id}
                  {...tierProps}
                />
              );
            })}
          </div>
        )}
        
        <div className="mt-16 text-center">
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
            Transaction fees only apply when a job is successfully booked — the higher your plan, the more you keep.
          </p>
          <p className="text-muted-foreground">
            Need a custom solution? <Link to="/contact" className="text-primary hover:underline">Contact our sales team</Link> for enterprise pricing and tailored packages.
          </p>
        </div>
      </div>
    </section>
    
    <section className="py-24 md:py-32 bg-background">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <h2 className="text-4xl md:text-5xl font-bold">Everything you need to succeed</h2>
          <p className="mt-6 text-xl text-muted-foreground">
            Our comprehensive platform provides all the tools and resources needed to grow your service business.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-10">
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg transition-all hover:shadow-xl">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Briefcase className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-2xl font-semibold mb-4">Find Local Jobs</h3>
            <p className="text-muted-foreground text-lg">
              Browse and bid on local jobs in your service area. Filter by location, price, and service type.
            </p>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="link" className="text-primary font-medium inline-flex items-center mt-6 text-lg p-0">
                  Find Jobs <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="bg-background border-border">
                <DropdownMenuItem asChild>
                  <Link to="/jobs" className="cursor-pointer">Browse All Jobs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/jobs/plumbing" className="cursor-pointer">Plumbing Jobs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/jobs/electrical" className="cursor-pointer">Electrical Jobs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/jobs/landscaping" className="cursor-pointer">Landscaping Jobs</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg transition-all hover:shadow-xl">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Calendar className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-2xl font-semibold mb-4">Join Us</h3>
            <p className="text-muted-foreground text-lg">
              Automate your booking system and sync with your existing calendar to manage appointments efficiently.
            </p>
            {(() => {
              const buttonConfig = getGeneralButtonConfig();
              if (buttonConfig.isStripe && buttonConfig.plan) {
                return (
                  <button
                    onClick={() => handleStripeCheckout(buttonConfig.plan!)}
                    disabled={isCheckoutLoading}
                    className="text-primary font-medium inline-flex items-center mt-6 text-lg hover:underline"
                  >
                    {buttonConfig.text}
                    <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </button>
                );
              }
              return (
                <Link to={buttonConfig.destination!} className="text-primary font-medium inline-flex items-center mt-6 text-lg">
                  {buttonConfig.text}
                  <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
              );
            })()}
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg transition-all hover:shadow-xl">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Wrench className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-2xl font-semibold mb-4">Free Business Tools</h3>
            <p className="text-muted-foreground text-lg">
              Access calculators, templates, and resources to help manage and grow your service business.
            </p>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="link" className="text-primary font-medium inline-flex items-center mt-6 text-lg p-0">
                  All Free Tools <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="bg-background border-border">
                <DropdownMenuItem asChild>
                  <Link to="/free-tools" className="cursor-pointer">All Tools</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/free-tools/pricing-calculator" className="cursor-pointer">Pricing Calculator</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/free-tools/invoice-templates" className="cursor-pointer">Invoice Templates</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/free-tools/marketing-guide" className="cursor-pointer">Marketing Guide</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </section>
    
    <section className="py-24 md:py-32 bg-muted/30">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <h2 className="text-4xl md:text-5xl font-bold">Why service providers choose JobON</h2>
          <p className="mt-6 text-xl text-muted-foreground">
            Join thousands of professionals growing their businesses with our platform
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-x-16 gap-y-14">
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Grow Your Business</h3>
              <p className="text-muted-foreground text-lg">Access a steady stream of qualified leads and opportunities in your service area.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <BadgeDollarSign className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Faster Payments</h3>
              <p className="text-muted-foreground text-lg">Get paid quickly with our secure payment processing system and flexible payout options.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Zap className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Boost Efficiency</h3>
              <p className="text-muted-foreground text-lg">Streamline operations with our scheduling tools, saving you time and reducing no-shows.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Shield className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Build Trust</h3>
              <p className="text-muted-foreground text-lg">Earn reviews, showcase your work, and build credibility with potential customers.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Users className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Customer Management</h3>
              <p className="text-muted-foreground text-lg">Keep track of client history, preferences, and communications in one central location.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Award className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Stand Out</h3>
              <p className="text-muted-foreground text-lg">Showcase your expertise with verified badges and enhanced profile visibility.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section className="py-24 md:py-32 bg-background">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <h2 className="text-4xl md:text-5xl font-bold">Trusted by service providers</h2>
          <p className="mt-6 text-xl text-muted-foreground">
            Hear from professionals who have grown their business with JobON
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-10">
          <div className="bg-card border border-border rounded-2xl p-8 shadow-lg transition-all hover:shadow-xl">
            <div className="flex items-center mb-6">
              <div className="text-amber-400 flex">
                {[...Array(5)].map((_, i) => <svg key={i} className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>)}
              </div>
            </div>
            <p className="mb-6 text-foreground text-lg">
              "JobON has been a game-changer for my plumbing business. I'm getting quality leads and the job matching system has reduced my administrative work by 50%."
            </p>
            <div className="flex items-center">
              <div className="rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center text-primary font-semibold text-lg">
                MS
              </div>
              <div className="ml-4">
                <p className="font-semibold text-lg">Mike Smith</p>
                <p className="text-muted-foreground">Smith Plumbing Services</p>
              </div>
            </div>
          </div>
          
          <div className="bg-card border border-border rounded-2xl p-8 shadow-lg transition-all hover:shadow-xl">
            <div className="flex items-center mb-6">
              <div className="text-amber-400 flex">
                {[...Array(5)].map((_, i) => <svg key={i} className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>)}
              </div>
            </div>
            <p className="mb-6 text-foreground text-lg">
              "The Pro plan gives me a real advantage with more job matches. I'm able to bid on 25 jobs per month, which has significantly increased my win rate."
            </p>
            <div className="flex items-center">
              <div className="rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center text-primary font-semibold text-lg">
                JD
              </div>
              <div className="ml-4">
                <p className="font-semibold text-lg">Jennifer Davis</p>
                <p className="text-muted-foreground">Clean & Clear Housekeeping</p>
              </div>
            </div>
          </div>
          
          <div className="bg-card border border-border rounded-2xl p-8 shadow-lg transition-all hover:shadow-xl">
            <div className="flex items-center mb-6">
              <div className="text-amber-400 flex">
                {[...Array(5)].map((_, i) => <svg key={i} className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>)}
              </div>
            </div>
            <p className="mb-6 text-foreground text-lg">
              "Unlimited job matches with the Elite plan have transformed our business. With only 5% transaction fees, we keep more of what we earn on every successful project."
            </p>
            <div className="flex items-center">
              <div className="rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center text-primary font-semibold text-lg">
                RJ
              </div>
              <div className="ml-4">
                <p className="font-semibold text-lg">Robert Johnson</p>
                <p className="text-muted-foreground">Green Acres Landscaping</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section className="py-24 md:py-32 bg-muted/30">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <h2 className="text-4xl md:text-5xl font-bold">Frequently asked questions</h2>
          <p className="mt-6 text-xl text-muted-foreground">
            Everything you need to know about joining JobON as a service provider
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-10 max-w-5xl mx-auto">
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">How do I get started with JobON?</h3>
            <p className="text-muted-foreground text-lg">
              Simply create a free Starter account, complete your profile, and start browsing available jobs. You can upgrade to Pro or Elite at any time to access more job matches and lower transaction fees.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">What are the key differences between plans?</h3>
            <p className="text-muted-foreground text-lg">
              Starter gives you 5 job matches/month with 15% fees, Pro gives you 25 matches/month with 10% fees, and Elite offers unlimited matches with only 5% transaction fees.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">How do job matches work?</h3>
            <p className="text-muted-foreground text-lg">
              Job matches are opportunities to bid on projects in your area. Each plan includes a specific number of monthly matches, with higher tiers offering more opportunities to win work.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">When do I pay transaction fees?</h3>
            <p className="text-muted-foreground text-lg">
              Transaction fees only apply when you successfully complete a job and get paid. No job completion means no fees – you only pay when you earn.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">What's included with SmartMatch™?</h3>
            <p className="text-muted-foreground text-lg">
              Available on Pro and Elite plans, SmartMatch™ uses AI to recommend jobs that perfectly fit your skills, location, and availability, increasing your win rate.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">Can I change my subscription plan?</h3>
            <p className="text-muted-foreground text-lg">
              Yes, you can upgrade or downgrade your plan at any time. Changes will take effect at the start of your next billing cycle.
            </p>
          </div>
        </div>
        
        <div className="text-center mt-14">
          <p className="text-muted-foreground text-lg">
            Have more questions? Visit our <Link to="/faq" className="text-primary hover:underline">FAQ page</Link> or <Link to="/contact" className="text-primary hover:underline">contact our support team</Link>.
          </p>
        </div>
      </div>
    </section>
    
    <section className="py-24">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="bg-primary/10 border border-primary/20 rounded-3xl p-10 md:p-16 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-primary/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>
          
          <div className="relative z-10 max-w-3xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-bold">Ready to grow your service business?</h2>
            <p className="mt-6 text-xl text-muted-foreground">
              Join thousands of service providers using JobON to find jobs, manage schedules, and grow their business.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-6 justify-center">
              {(() => {
                const buttonConfig = getGeneralButtonConfig();
                if (buttonConfig.isStripe && buttonConfig.plan) {
                  return (
                    <Button
                      size="lg"
                      className="py-7 px-10 text-lg"
                      onClick={() => handleStripeCheckout(buttonConfig.plan!)}
                      disabled={isCheckoutLoading}
                    >
                      {buttonConfig.text}
                    </Button>
                  );
                }
                return (
                  <Button size="lg" className="py-7 px-10 text-lg" asChild>
                    <Link to={buttonConfig.destination!}>{buttonConfig.text}</Link>
                  </Button>
                );
              })()}
              <Button variant="outline" size="lg" className="py-7 px-10 text-lg" asChild>
                <Link to="/contact">Request a Demo</Link>
              </Button>
            </div>
            <p className="mt-8 text-muted-foreground">
              No credit card required to start. Try the Starter plan for free.
            </p>
          </div>
        </div>
      </div>
    </section>
  </Layout>;
};

export default ForProviders;
