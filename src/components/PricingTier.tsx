
import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, X } from "lucide-react";
import { Link } from "react-router-dom";

interface PricingFeature {
  included: boolean | string;
  text: string;
  icon: React.ReactNode;
}

interface PricingTierProps {
  title: string;
  price: string;
  yearlyPrice?: string;
  description: string;
  jobMatches: number | "Unlimited";
  transactionFee: string;
  features: PricingFeature[];
  ctaText: string;
  ctaDestination?: string;
  ctaDisabled?: boolean;
  ctaOnClick?: () => void;
  popular?: boolean;
  highlighted?: boolean;
  isYearly?: boolean;
  showSavingsBadge?: boolean;
}

const PricingTier: React.FC<PricingTierProps> = ({
  title,
  price,
  yearlyPrice,
  description,
  jobMatches,
  transactionFee,
  features,
  ctaText,
  ctaDestination = "/auth",
  ctaDisabled = false,
  ctaOnClick,
  popular = false,
  highlighted = false,
  isYearly = false,
  showSavingsBadge = false,
}) => {
  const displayPrice = isYearly && yearlyPrice ? yearlyPrice : price;
  const originalPrice = isYearly ? price : undefined;

  return (
    <div className={`relative flex flex-col h-full rounded-2xl shadow-lg bg-card border transition-all ${highlighted ? "border-primary" : "border-border"}`}>
      {popular && (
        <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 py-1 px-3 bg-primary hover:bg-primary">
          Most Popular
        </Badge>
      )}
      
      <div className="p-8 border-b border-border space-y-4">
        <h3 className="text-2xl font-bold">{title}</h3>
        <div className="flex items-baseline gap-2">
          <span className="text-4xl font-bold">
            {displayPrice === "Free" || displayPrice === "0" ? "Free" : `$${displayPrice}`}
          </span>
          {displayPrice !== "Free" && displayPrice !== "0" && (
            <span className="text-muted-foreground">/month</span>
          )}
          {showSavingsBadge && isYearly && originalPrice && (
            <Badge variant="success" className="ml-2">Save 15%</Badge>
          )}
        </div>
        {isYearly && originalPrice && displayPrice !== originalPrice && (
          <p className="text-sm text-muted-foreground">
            Billed annually at ${(Number(displayPrice) * 12).toLocaleString()}
          </p>
        )}
        <p className="text-muted-foreground">{description}</p>
      </div>
      
      <div className="p-8 border-b border-border">
        <div className="space-y-4 mb-6">
          <div className="flex items-center gap-2">
            <span className="text-primary text-lg font-semibold">
              {typeof jobMatches === "number" ? jobMatches : jobMatches} job matches
            </span>
            <span className="text-muted-foreground">per month</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-primary text-lg font-semibold">{transactionFee}</span>
            <span className="text-muted-foreground">transaction fee</span>
          </div>
        </div>
        
        <div className="space-y-5">
          {features.map((feature, idx) => (
            <div key={idx} className="flex gap-4">
              {feature.icon}
              <div>
                {typeof feature.included === "boolean" ? (
                  <div className="flex gap-2 items-center">
                    {feature.included ? 
                      <Check className="h-5 w-5 text-green-500" /> : 
                      <X className="h-5 w-5 text-gray-300" />
                    }
                    <span className={!feature.included ? "text-muted-foreground" : ""}>{feature.text}</span>
                  </div>
                ) : (
                  <div>
                    <div className="font-medium">{feature.included}</div>
                    <div className="text-muted-foreground">{feature.text}</div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="p-8 mt-auto">
        {ctaDisabled ? (
          <Button className="w-full"
                  variant={highlighted ? "default" : "outline"}
                  disabled>
            {ctaText}
          </Button>
        ) : ctaOnClick ? (
          <Button className="w-full"
                  variant={highlighted ? "default" : "outline"}
                  onClick={ctaOnClick}>
            {ctaText}
          </Button>
        ) : (
          <Button className="w-full"
                  variant={highlighted ? "default" : "outline"}
                  asChild>
            <Link to={ctaDestination}>{ctaText}</Link>
          </Button>
        )}
      </div>
    </div>
  );
};

export default PricingTier;
