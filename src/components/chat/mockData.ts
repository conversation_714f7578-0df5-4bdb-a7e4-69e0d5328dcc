import { Chat, Message } from '@/types/chat';

/**
 * Mock data for chat testing and development
 */

export const mockChats: Chat[] = [
  {
    id: 'chat-1',
    type: 'direct',
    name: null,
    description: null,
    participants: [
      {
        id: 'provider-1',
        name: "<PERSON>'s Plumbing",
        role: { name: 'provider' },
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        is_online: true,
        last_seen: null,
      },
      {
        id: 'customer-1',
        name: '<PERSON>',
        role: { name: 'customer' },
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        is_online: false,
        last_seen: '2 hours ago',
      }
    ],
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T14:30:00Z',
    last_message: {
      id: 'msg-3',
      chat_id: 'chat-1',
      user_id: 'provider-1',
      type: 'text',
      message: 'I can start the bathroom renovation next Monday. What time works best for you?',
      created_at: '2024-01-15T14:30:00Z',
      updated_at: '2024-01-15T14:30:00Z',
      user: {
        id: 'provider-1',
        name: "Mike's Plumbing",
        type: 'provider',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      }
    },
    unread_count: 1,
  },
  {
    id: 'chat-2',
    type: 'direct',
    name: null,
    description: null,
    participants: [
      {
        id: 'provider-2',
        name: 'Green Thumb Landscaping',
        role: { name: 'provider' },
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
        is_online: true,
        last_seen: null,
      },
      {
        id: 'customer-1',
        name: 'John Smith',
        role: { name: 'customer' },
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        is_online: false,
        last_seen: '2 hours ago',
      }
    ],
    created_at: '2024-01-14T09:00:00Z',
    updated_at: '2024-01-14T16:45:00Z',
    last_message: {
      id: 'msg-6',
      chat_id: 'chat-2',
      user_id: 'customer-1',
      type: 'text',
      message: 'Thanks for the quote! When can you start the lawn maintenance?',
      created_at: '2024-01-14T16:45:00Z',
      updated_at: '2024-01-14T16:45:00Z',
      user: {
        id: 'customer-1',
        name: 'John Smith',
        type: 'customer',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      }
    },
    unread_count: 0,
  },
  {
    id: 'chat-3',
    type: 'group',
    name: 'Kitchen Renovation Project',
    description: 'Discussion about the kitchen renovation project',
    participants: [
      {
        id: 'admin-1',
        name: 'JobON Support',
        role: { name: 'admin' },
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        is_online: true,
        last_seen: null,
      },
      {
        id: 'provider-3',
        name: 'Elite Contractors',
        role: { name: 'provider' },
        avatar: 'https://images.unsplash.com/photo-**********-0b93528c311a?w=150&h=150&fit=crop&crop=face',
        is_online: false,
        last_seen: '1 hour ago',
      },
      {
        id: 'customer-1',
        name: 'John Smith',
        role: { name: 'customer' },
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        is_online: false,
        last_seen: '2 hours ago',
      }
    ],
    created_at: '2024-01-13T08:00:00Z',
    updated_at: '2024-01-13T17:20:00Z',
    last_message: {
      id: 'msg-9',
      chat_id: 'chat-3',
      user_id: 'admin-1',
      type: 'text',
      message: 'I\'ve reviewed the project details. Everything looks good to proceed!',
      created_at: '2024-01-13T17:20:00Z',
      updated_at: '2024-01-13T17:20:00Z',
      user: {
        id: 'admin-1',
        name: 'JobON Support',
        type: 'admin',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      }
    },
    unread_count: 2,
  }
];

export const mockMessages: Record<string, Message[]> = {
  'chat-1': [
    {
      id: 'msg-1',
      chat_id: 'chat-1',
      user_id: 'customer-1',
      type: 'text',
      message: 'Hi! I need help with my bathroom renovation. Can you provide a quote?',
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-15T10:00:00Z',
      user: {
        id: 'customer-1',
        name: 'John Smith',
        type: 'customer',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      }
    },
    {
      id: 'msg-2',
      chat_id: 'chat-1',
      user_id: 'provider-1',
      type: 'text',
      message: 'Hello John! I\'d be happy to help with your bathroom renovation. Could you share some details about what you\'re looking to do?',
      created_at: '2024-01-15T10:15:00Z',
      updated_at: '2024-01-15T10:15:00Z',
      user: {
        id: 'provider-1',
        name: "Mike's Plumbing",
        type: 'provider',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      }
    },
    {
      id: 'msg-3',
      chat_id: 'chat-1',
      user_id: 'provider-1',
      type: 'text',
      message: 'I can start the bathroom renovation next Monday. What time works best for you?',
      created_at: '2024-01-15T14:30:00Z',
      updated_at: '2024-01-15T14:30:00Z',
      user: {
        id: 'provider-1',
        name: "Mike's Plumbing",
        type: 'provider',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      }
    }
  ],
  'chat-2': [
    {
      id: 'msg-4',
      chat_id: 'chat-2',
      user_id: 'provider-2',
      type: 'text',
      message: 'Hi! I saw your lawn maintenance request. I can provide weekly service for $80/month.',
      created_at: '2024-01-14T09:00:00Z',
      updated_at: '2024-01-14T09:00:00Z',
      user: {
        id: 'provider-2',
        name: 'Green Thumb Landscaping',
        type: 'provider',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
      }
    },
    {
      id: 'msg-5',
      chat_id: 'chat-2',
      user_id: 'customer-1',
      type: 'text',
      message: 'That sounds reasonable. What does the service include?',
      created_at: '2024-01-14T12:30:00Z',
      updated_at: '2024-01-14T12:30:00Z',
      user: {
        id: 'customer-1',
        name: 'John Smith',
        type: 'customer',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      }
    },
    {
      id: 'msg-6',
      chat_id: 'chat-2',
      user_id: 'customer-1',
      type: 'text',
      message: 'Thanks for the quote! When can you start the lawn maintenance?',
      created_at: '2024-01-14T16:45:00Z',
      updated_at: '2024-01-14T16:45:00Z',
      user: {
        id: 'customer-1',
        name: 'John Smith',
        type: 'customer',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      }
    }
  ],
  'chat-3': [
    {
      id: 'msg-7',
      chat_id: 'chat-3',
      user_id: 'customer-1',
      type: 'text',
      message: 'I\'m planning a kitchen renovation and would like to get started.',
      created_at: '2024-01-13T08:00:00Z',
      updated_at: '2024-01-13T08:00:00Z',
      user: {
        id: 'customer-1',
        name: 'John Smith',
        type: 'customer',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      }
    },
    {
      id: 'msg-8',
      chat_id: 'chat-3',
      user_id: 'provider-3',
      type: 'text',
      message: 'Great! I specialize in kitchen renovations. What\'s your budget and timeline?',
      created_at: '2024-01-13T10:30:00Z',
      updated_at: '2024-01-13T10:30:00Z',
      user: {
        id: 'provider-3',
        name: 'Elite Contractors',
        type: 'provider',
        avatar: 'https://images.unsplash.com/photo-**********-0b93528c311a?w=150&h=150&fit=crop&crop=face',
      }
    },
    {
      id: 'msg-9',
      chat_id: 'chat-3',
      user_id: 'admin-1',
      type: 'text',
      message: 'I\'ve reviewed the project details. Everything looks good to proceed!',
      created_at: '2024-01-13T17:20:00Z',
      updated_at: '2024-01-13T17:20:00Z',
      user: {
        id: 'admin-1',
        name: 'JobON Support',
        type: 'admin',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      }
    }
  ]
};

// Helper function to get mock data based on user role
export function getMockChatsForRole(userRole: 'admin' | 'provider' | 'customer', userId: string): Chat[] {
  // For development/testing, if userId is generic (like 'provider-user'),
  // return all chats that have participants of the matching role
  const isGenericUserId = userId.endsWith('-user');

  return mockChats.filter(chat =>
    chat.participants.some(participant => {
      // Exact user ID match
      if (participant.id === userId) {
        return true;
      }

      // For generic user IDs, match by role name
      if (isGenericUserId && participant.role?.name === userRole) {
        return true;
      }

      return false;
    })
  );
}

export function getMockMessagesForChat(chatId: string): Message[] {
  return mockMessages[chatId] || [];
}
