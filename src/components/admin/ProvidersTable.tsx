
import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import { nullToUndefined } from '@/utils/typeHelpers';
import { providerService, Provider } from '@/services/providerService';
import { chatService } from '@/services/chatService';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MoreHorizontal, MessageSquare, Trash2, Eye, Building2, Link, Link2Off, UserCog, Search, X } from "lucide-react";
import { Pagination } from "@/components/ui/pagination";
import { useDebounceValue } from "@/hooks/use-debounce";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import BusinessLinkingDialog from "./BusinessLinkingDialog";
import BusinessUnlinkingDialog from "./BusinessUnlinkingDialog";
import { ProviderTierManagementForm } from "./provider-tier/ProviderTierManagementForm";



enum StatusCertificates {
    APPROVE= 'approved',
    REJECT= 'rejected',
    PENDING= 'requested'
}


const ProvidersTableComponent: React.FC = () => {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [providerToDelete, setProviderToDelete] = useState<Provider | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [providerStats, setProviderStats] = useState<any | null>(null);
  const [isBusinessLinkingOpen, setIsBusinessLinkingOpen] = useState(false);
  const [isBusinessUnlinkingOpen, setIsBusinessUnlinkingOpen] = useState(false);
  const [isCreateChatModalOpen, setIsCreateChatModalOpen] = useState(false);
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const [isAssignPlansOpen, setIsAssignPlansOpen] = useState(false);

  // Pagination and search states
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10); // setPerPage kept for future per-page selector
  const [totalItems, setTotalItems] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  // Create debounced version of the search query
  const debouncedSearchQuery = useDebounceValue(searchQuery, 800);

  const authHeader = useAuthHeader();
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { isMobile } = useUIHelpers();
  // Reset to first page when search query changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchQuery]);

  // Fetch providers function with pagination and search
  const fetchProviders = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await providerService.getProviders(
        currentPage,
        perPage,
        debouncedSearchQuery || undefined,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess && response.data) {
        setProviders(response.data.data);
        setTotalItems(response.data.meta.total);
        // setCurrentPage(response.data.meta.current_page);
      } else {
        toast.error(response.error || 'Failed to fetch providers');
      }
    } catch (error) {
      toast.error('Failed to fetch providers');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, debouncedSearchQuery, perPage, authHeader]);

  // Fetch providers on component mount and when dependencies change
  useEffect(() => {
    fetchProviders();
  }, [currentPage, debouncedSearchQuery, perPage]);

  // Handle certificate approval redirect
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const certificateApproval = urlParams.get('certificateApproval');
    const certificateId = urlParams.get('certificateId');
    const providerId = urlParams.get('providerId');
    const providerName = urlParams.get('providerName');
    const providerEmail = urlParams.get('providerEmail');

    if (certificateApproval === 'true' && certificateId && providerId && providerName && providerEmail) {
      // Create a mock provider object from the URL parameters for the BusinessLinkingDialog
      const mockProvider: Provider = {
        id: providerId,
        name: decodeURIComponent(providerName),
        email: decodeURIComponent(providerEmail),
        phone: '',
        role_id: 0,
        created_at: '',
        updated_at: '',
        certificates_status: 'approved'
      };

      // Set the selected provider and open the BusinessLinkingDialog
      setSelectedProvider(mockProvider);
      setIsBusinessLinkingOpen(true);

      // Store certificate ID for potential rejection
      sessionStorage.setItem('pendingCertificateId', certificateId);

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);

      // Show notification
      toast.success('Certificate approved! Please link businesses to complete the process.');
    }
  }, [location.search]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Handle clear search
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    setCurrentPage(1);
  }, []);

  const fetchProviderStats = async (providerId: string) => {
    try {
      const response = await providerService.getProviderStats(providerId, nullToUndefined(authHeader) || '');

      if (response.isSuccess && response.data) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching provider stats:', error);
      return null;
    }
  };

  const handleViewDetails = async (provider: Provider) => {
    setSelectedProvider(provider);

    const stats = await fetchProviderStats(provider.id);
    if (stats) {
      setProviderStats(stats);
    }

    setIsDetailModalOpen(true);
  };

  // Handle provider deletion with proper type handling
  const handleDeleteProvider = async () => {
    if (!providerToDelete) return;

    setIsDeleting(true);
    try {
      const response = await providerService.deleteProvider(
        providerToDelete.id,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess) {
        toast.success('Provider deleted successfully');
        // Refresh the current page to get updated data
        fetchProviders();
        setIsDeleteModalOpen(false);
        setProviderToDelete(null);
      } else {
        toast.error(response.error || 'Failed to delete provider');
      }
    } catch (error) {
      console.error('Error deleting provider:', error);
      toast.error('Failed to delete provider');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle send message to provider
  const handleSendMessage = (provider: Provider) => {
    setSelectedProvider(provider);
    setIsCreateChatModalOpen(true);
  };

  // Handle creating chat room with provider
  const handleCreateChat = async () => {
    if (!selectedProvider || !user || !user.id) {
      toast.error('Unable to identify current user. Please try logging in again.');
      return;
    }

    setIsCreatingChat(true);

    try {
      const response = await chatService.createChat(
        selectedProvider.id,
        'direct',
        undefined,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess && response.data) {
        toast.success(`Chat room created with ${selectedProvider.name}`);
        setIsCreateChatModalOpen(false);
        setSelectedProvider(null);
        navigate('/admin/messages');
      } else {
        if (response.error?.includes('already exists') || response.error?.includes('duplicate')) {
          toast.success(`Opening existing chat with ${selectedProvider.name}`);
          setIsCreateChatModalOpen(false);
          setSelectedProvider(null);
          navigate('/admin/messages');
        } else {
          toast.error(response.error || 'Failed to create chat room');
        }
      }
    } catch (error) {
      toast.error('An error occurred while creating the chat room');
    } finally {
      setIsCreatingChat(false);
    }
  };

  if (isLoading && providers.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading providers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Loading Overlay */}
      {isLoading && providers.length > 0 && (
        <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 z-10 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      )}

      {/* Search Bar */}
      <div className="mb-6">
        <div className="sm:flex gap-2 justify-between items-center pt-6 px-4">
          <h2 className="text-xl sm:text-2xl font-bold mb-2 sm:mb-0">Provider Management</h2>
          <div className="sm:w-[400px] w-full">
            {!isMobile ? (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search providers by name, email, or phone..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9 pr-8 py-2 bg-gray-50 border-gray-200"
                />
                {searchQuery && (
                  <div
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 cursor-pointer mr-3"
                    onClick={handleClearSearch}
                  >
                    <X className="h-4 w-4" />
                  </div>
                )}
              </div>
            ) : (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  className="pl-9 pr-8 py-2 rounded-full bg-gray-50 border-gray-200"
                  placeholder="Search providers..."
                  value={searchQuery}
                  onChange={(e) => {
                    setCurrentPage(1)
                    setSearchQuery(e.target.value)
                  } }
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7"
                    onClick={handleClearSearch}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Desktop Table View */}
      <div className="hidden md:block px-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Business Linked</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {providers.length === 0 && !isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-12">
                  <div className="text-gray-500 dark:text-gray-400">
                    {debouncedSearchQuery ? (
                      <>
                        <p className="text-lg font-medium mb-2">No providers found</p>
                        <p>No providers match your search for "{debouncedSearchQuery}"</p>
                        <Button
                          variant="outline"
                          onClick={handleClearSearch}
                          className="mt-4"
                        >
                          Clear search
                        </Button>
                      </>
                    ) : (
                      <>
                        <p className="text-lg font-medium mb-2">No providers yet</p>
                        <p>Providers will appear here once they register</p>
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              providers.map((provider) => (
              <TableRow key={provider.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar>
                      <AvatarImage src={provider.avatar || "/placeholder.svg"} alt={provider.name} />
                      <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    {provider.name}
                  </div>
                </TableCell>
                <TableCell>{provider.email}</TableCell>
                <TableCell>{provider.phone}</TableCell>
                <TableCell>
                  <a className="text-blue-600 underline" href={provider?.business?.website} target="_blank">{provider?.business?.name}</a>
                </TableCell>
                <TableCell>
                  {provider.certificates_status ? (
                    <Badge
                      variant="outline"
                      className={
                        provider.certificates_status === StatusCertificates.APPROVE
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 capitalize"
                          : provider.certificates_status === StatusCertificates.REJECT
                            ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 capitalize"
                            : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 capitalize"
                      }
                    >
                      {provider.certificates_status}
                    </Badge>
                  ) : <Badge variant="outline">No request</Badge>}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(provider)}>
                        <Eye className="mr-2 h-4 w-4" /> View Details
                      </DropdownMenuItem>
                      {
                        provider.business ? <DropdownMenuItem onClick={() => {
                          setSelectedProvider(provider);
                          setIsBusinessUnlinkingOpen(true);
                        }}>
                          <Link2Off className="mr-2 h-4 w-4" /> Unlink Business
                        </DropdownMenuItem> :
                        (provider.certificates_status !== StatusCertificates.REJECT && provider.certificates_status != null) && (
                          <DropdownMenuItem onClick={() => {
                            setSelectedProvider(provider);
                            setIsBusinessLinkingOpen(true);
                          }}>
                            <Building2 className="mr-2 h-4 w-4" /> Link Business
                          </DropdownMenuItem>
                        )
                      }
                      <DropdownMenuItem onClick={() => {
                        setSelectedProvider(provider);
                        setIsAssignPlansOpen(true);
                      }}>
                        <UserCog className="mr-2 h-4 w-4" /> Assign Plans
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleSendMessage(provider)}>
                        <MessageSquare className="mr-2 h-4 w-4" /> Send Message
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          setProviderToDelete(provider);
                          setIsDeleteModalOpen(true);
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" /> Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        {/* Desktop Pagination */}
        {totalItems > 0 && !isLoading && (
          <div className="mt-6">
            <Pagination
              totalItems={totalItems}
              itemsPerPage={perPage}
              currentPage={currentPage}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4 px-4">
        {providers.length === 0 && !isLoading ? (
          <div className="text-center py-12">
            <div className="text-gray-500 dark:text-gray-400">
              {debouncedSearchQuery ? (
                <>
                  <p className="text-lg font-medium mb-2">No providers found</p>
                  <p>No providers match your search for "{debouncedSearchQuery}"</p>
                  <Button
                    variant="outline"
                    onClick={handleClearSearch}
                    className="mt-4"
                  >
                    Clear search
                  </Button>
                </>
              ) : (
                <>
                  <p className="text-lg font-medium mb-2">No providers yet</p>
                  <p>Providers will appear here once they register</p>
                </>
              )}
            </div>
          </div>
        ) : (
          providers.map((provider) => (
          <div key={provider.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={provider.avatar || "/placeholder.svg"} alt={provider.name} />
                  <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">{provider.name}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">{provider.email}</div>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleViewDetails(provider)}>
                    <Eye className="mr-2 h-4 w-4" /> View Details
                  </DropdownMenuItem>
                  {
                    provider.business ? <DropdownMenuItem onClick={() => {
                      setSelectedProvider(provider);
                      setIsBusinessUnlinkingOpen(true);
                    }}>
                      <Link2Off className="mr-2 h-4 w-4" /> Unlink Business
                    </DropdownMenuItem> :
                    (provider.certificates_status !== StatusCertificates.REJECT && provider.certificates_status != null) && (
                      <DropdownMenuItem onClick={() => {
                        setSelectedProvider(provider);
                        setIsBusinessLinkingOpen(true);
                      }}>
                        <Building2 className="mr-2 h-4 w-4" /> Link Business
                      </DropdownMenuItem>
                    )
                  }
                  <DropdownMenuItem onClick={() => {
                    setSelectedProvider(provider);
                    setIsAssignPlansOpen(true);
                  }}>
                    <UserCog className="mr-2 h-4 w-4" /> Assign Plans
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSendMessage(provider)}>
                    <MessageSquare className="mr-2 h-4 w-4" /> Send Message
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      setProviderToDelete(provider);
                      setIsDeleteModalOpen(true);
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Phone:</span>
                <span className="text-gray-900 dark:text-gray-100">{provider.phone}</span>
              </div>

              {provider?.business?.name && (
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Business:</span>
                  <a className="text-blue-600 underline" href={provider?.business?.website} target="_blank">
                    {provider?.business?.name}
                  </a>
                </div>
              )}

              <div className="flex justify-between items-center">
                <span className="text-gray-500 dark:text-gray-400">Status:</span>
                {provider.certificates_status ? (
                  <Badge
                    variant="outline"
                    className={
                      provider.certificates_status === StatusCertificates.APPROVE
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 capitalize"
                        : provider.certificates_status === StatusCertificates.REJECT
                          ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 capitalize"
                          : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 capitalize"
                    }
                  >
                    {provider.certificates_status}
                  </Badge>
                ) : <Badge variant="outline">No request</Badge>}
              </div>
            </div>
          </div>
          ))
        )}

        {/* Mobile Pagination */}
        {totalItems > 0 && !isLoading && (
          <div className="mt-6">
            <Pagination
              totalItems={totalItems}
              itemsPerPage={perPage}
              currentPage={currentPage}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* Provider Details Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>Provider Details</DialogTitle>
            <DialogDescription>
              View detailed information about the selected provider.
            </DialogDescription>
          </DialogHeader>
          {selectedProvider && providerStats && (
            <ScrollArea className="h-[400px] w-full space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-lg font-semibold">Personal Information</div>
                  <div className="mt-2 space-y-1">
                    <p><strong>Name:</strong> {selectedProvider.name}</p>
                    <p><strong>Email:</strong> {selectedProvider.email}</p>
                    <p><strong>Phone:</strong> {selectedProvider.phone}</p>
                    <p><strong>Location:</strong> {selectedProvider.location}</p>
                  </div>
                </div>
                <div>
                  <div className="text-lg font-semibold">Business Information</div>
                  <div className="mt-2 space-y-1">
                    <p><strong>Business Name:</strong> {selectedProvider.business_name || 'Not linked'}</p>
                    <p><strong>Category:</strong> {selectedProvider.category || 'N/A'}</p>
                    <p><strong>Specialization:</strong> {selectedProvider.specialty || 'N/A'}</p>
                    <div className="flex space-x-2 mt-2">
                      {(selectedProvider.certificates_status !== StatusCertificates.REJECT && selectedProvider.certificates_status != null) && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setIsBusinessLinkingOpen(true)}
                        >
                          <Link className="mr-2 h-4 w-4" /> Link Business
                        </Button>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsBusinessUnlinkingOpen(true)}
                      >
                        <Link2Off className="mr-2 h-4 w-4" /> Unlink Business
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="text-lg font-semibold">Statistics</div>
                <div className="mt-2 space-y-1">
                  <p><strong>Total Jobs:</strong> {providerStats.totalJobs}</p>
                  <p><strong>Average Rating:</strong> {providerStats.averageRating}</p>
                  <p><strong>Revenue:</strong> ${providerStats.revenue}</p>
                </div>
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the provider.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isDeleting}
              onClick={handleDeleteProvider}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Business Linking Dialog */}
      <BusinessLinkingDialog
        isOpen={isBusinessLinkingOpen}
        onClose={() => setIsBusinessLinkingOpen(false)}
        provider={selectedProvider}
        onSuccess={() => {
          // Refresh providers data after successful business linking
          fetchProviders();
          setIsBusinessLinkingOpen(false);
        }}
      />

      {/* Business Unlinking Dialog */}
      <BusinessUnlinkingDialog
        isOpen={isBusinessUnlinkingOpen}
        onClose={() => setIsBusinessUnlinkingOpen(false)}
        provider={selectedProvider}
        onSuccess={() => {
          // Refresh providers data after successful business unlinking
          fetchProviders();
          setIsBusinessUnlinkingOpen(false);
        }}
      />

      {/* Create Chat Confirmation Dialog */}
      <AlertDialog open={isCreateChatModalOpen} onOpenChange={setIsCreateChatModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Create Chat Room</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedProvider && (
                <>
                  Create a direct chat room with <strong>{selectedProvider.name}</strong>?
                  This will allow you to communicate directly with this provider.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isCreatingChat}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isCreatingChat}
              onClick={handleCreateChat}
            >
              {isCreatingChat ? "Creating..." : "Create Chat Room"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Assign Plans Dialog */}
      <Dialog open={isAssignPlansOpen} onOpenChange={setIsAssignPlansOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Assign Plan to Provider</DialogTitle>
            <DialogDescription>
              {selectedProvider && (
                <>
                  Assign subscription plans to <strong>{selectedProvider.name}</strong> and manage their tier status.
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            <ProviderTierManagementForm 
              selectedProvider={selectedProvider || undefined}
              onSuccess={() => {
                // Refresh providers data after successful plan assignment
                fetchProviders();
                setIsAssignPlansOpen(false);
                setSelectedProvider(null);
              }}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Optimize with React.memo for better performance
export const ProvidersTable = React.memo(ProvidersTableComponent);

// Add displayName for debugging
ProvidersTable.displayName = 'ProvidersTable';
