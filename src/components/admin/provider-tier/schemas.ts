
import { z } from "zod";

// Provider Tier Management Form Schema
export const providerTierManagementSchema = z.object({
  providerId: z.string().min(1, "Provider is required"),
  planId: z.string().min(1, "Subscription plan is required"),
  duration: z.enum(["monthly", "yearly"]).optional(),
  notes: z.string().max(500, "Notes cannot exceed 500 characters").optional(),
});

export type ProviderTierManagementFormValues = z.infer<typeof providerTierManagementSchema>;

// Provider Plan Schema
export const providerPlanSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Plan name is required"),
  price: z.number().min(0, "Price must be a positive number"),
  yearlyPrice: z.number().min(0, "Yearly price must be a positive number").optional(),
  description: z.string().min(1, "Description is required"),
  jobMatches: z.union([z.number().min(0), z.literal("Unlimited")]),
  transactionFee: z.string().min(1, "Transaction fee is required"),
  duration: z.enum(["monthly", "yearly"]).optional(),
  features: z.array(
    z.object({
      included: z.boolean(),
      text: z.string().min(1, "Feature description is required"),
    })
  ).min(1, "At least one feature is required"),
  // Stripe integration fields
  stripe_price_id: z.string().optional(),
  stripe_product_id: z.string().optional(),
});

export type ProviderPlan = z.infer<typeof providerPlanSchema>;
