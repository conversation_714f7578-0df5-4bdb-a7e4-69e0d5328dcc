import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, Di<PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Loader2, Search, Building2, Link, Link2Off } from "lucide-react";
import { toast } from "sonner";
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import { nullToUndefined } from '@/utils/typeHelpers';
import { businessService } from '@/services/businessService';
import { BusinessType } from '@/pages/Business';
import { Provider } from '@/services/providerService';
import { useDebounceValue } from '@/hooks/use-debounce';
import { apiService } from '@/services/api';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {useToast} from "@/hooks/use-toast.ts";

interface BusinessLinkingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  provider: Provider | null;
  onSuccess: () => void;
}

const BusinessLinkingDialog: React.FC<BusinessLinkingDialogProps> = ({
  isOpen,
  onClose,
  provider,
  onSuccess
}) => {
  const [businesses, setBusinesses] = useState<BusinessType[]>([]);
  const [linkedBusinesses, setLinkedBusinesses] = useState<BusinessType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [linkingBusinessIds, setLinkingBusinessIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounceValue(searchQuery, 500);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasLinkedBusiness, setHasLinkedBusiness] = useState(false);
  const [showCloseConfirmation, setShowCloseConfirmation] = useState(false);
  const [isRejectingCertificate, setIsRejectingCertificate] = useState(false);
  const authHeader = useAuthHeader();
    const { toast } = useToast();

  // Fetch businesses
  useEffect(() => {
    if (!isOpen || !provider) return;

    const fetchBusinesses = async () => {
      setIsLoading(true);
      try {
        const response = await businessService.getBusinesses(
          currentPage,
          10,
          debouncedSearchQuery,
          undefined,
          nullToUndefined(authHeader) || ''
        );

        if (response.isSuccess && response.data) {
          setBusinesses(response.data.data);
          setTotalPages(response.data.meta.last_page);
        }
      } catch (error) {
        console.error('Error fetching businesses:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBusinesses();
  }, [isOpen, provider, currentPage, debouncedSearchQuery, authHeader]);

  // Fetch linked businesses for the provider
  useEffect(() => {
    if (!isOpen || !provider) return;

    const fetchLinkedBusinesses = async () => {
      try {
        const response = await businessService.getProviderBusinesses(
          provider.id,
          nullToUndefined(authHeader) || ''
        );
      } catch (error) {
        console.error('Error fetching linked businesses:', error);
      }
    };

    fetchLinkedBusinesses();
  }, [isOpen, provider, authHeader]);

  // Check if a business is linked to the provider
  const isBusinessLinked = (businessId: string) => {
    return linkedBusinesses.some(business => business.businessId === businessId);
  };

  // Handle linking/unlinking a business to/from a provider
  const handleToggleLink = async (business: BusinessType) => {
    if (!provider) return;

    // Add this business to the linking set
    setLinkingBusinessIds(prev => new Set(prev).add(business.businessId));

    try {
      const isLinked = isBusinessLinked(business.businessId);
      const data = {
        provider_id: provider.id,
        business_uuid: business.businessId
      };

      let response;
      if (isLinked) {
        response = await businessService.unlinkBusinessFromProvider(
          data,
          nullToUndefined(authHeader) || ''
        );
      } else {
        response = await businessService.linkBusinessToProvider(
          data,
          nullToUndefined(authHeader) || ''
        );
      }

      if (response.isSuccess) {
        // Update the linked businesses list
        if (isLinked) {
          setLinkedBusinesses(prev => prev.filter(b => b.businessId !== business.businessId));
            toast({
                title: "Successfully!",
                description: `Successfully unlinked ${business.name} from ${provider.name}`,
                className: "bg-green-400 text-white",
            });
        } else {
          setLinkedBusinesses(prev => [...prev, business]);
          setHasLinkedBusiness(true); // Mark that a business was successfully linked
            toast({
                title: "Successfully!",
                description: `Successfully linked ${business.name} to ${provider.name}`,
                className: "bg-green-400 text-white",
            });

          // Clean up pending certificate ID since linking was successful
          sessionStorage.removeItem('pendingCertificateId');

          // Close the modal after successful linking
          onClose();
        }

        // Call the onSuccess callback to refresh the provider details if needed
        onSuccess();
      } else {
        // Show specific error message from API or generic message
        const errorMessage = response.error || `Failed to ${isLinked ? 'unlink' : 'link'} ${business.name}`;
          toast({
              title: "Failed!",
              description: errorMessage,
              variant: "destructive"
          });
      }
    } catch (error) {
      console.error(`Error ${isBusinessLinked(business.businessId) ? 'unlinking' : 'linking'} business:`, error);
      const action = isBusinessLinked(business.businessId) ? 'unlink' : 'link';
        toast({
            title: "Failed!",
            description: `Failed to ${action} ${business.name}. Please try again.`,
            variant: "destructive"
        });
    } finally {
      // Remove this business from the linking set
      setLinkingBusinessIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(business.businessId);
        return newSet;
      });
    }
  };

  // Get business initials for avatar fallback
  const getBusinessInitials = (name: string) => {
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name.charAt(0).toUpperCase();
  };

  // Handle certificate rejection when dialog is closed without successful linking
  const handleCertificateRejection = async () => {
    const pendingCertificateId = sessionStorage.getItem('pendingCertificateId');

    if (pendingCertificateId && !hasLinkedBusiness) {
      setIsRejectingCertificate(true);
      try {
        const response = await apiService(`/api/admin/certificates/reviews/${pendingCertificateId}/reject`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': nullToUndefined(authHeader) || ''
          },
        });

        if (response.isSuccess) {
          toast({
            title: "Certificate Rejected",
            description: 'Certificate has been successfully rejected.',
            className: "bg-green-400 text-white",
          });
        } else {
          toast({
            title: "Failed to Reject",
            description: response.error || 'Failed to reject certificate. Please try again.',
            variant: "destructive"
          });
          console.error('Failed to reject certificate:', response.error);
        }
      } catch (error) {
        console.error('Error rejecting certificate:', error);
        toast({
          title: "Error",
          description: 'An unexpected error occurred while rejecting the certificate.',
          variant: "destructive"
        });
      } finally {
        setIsRejectingCertificate(false);
        sessionStorage.removeItem('pendingCertificateId');
      }
    }
  };

  // Enhanced close handler with confirmation
  const handleCloseRequest = () => {
    const pendingCertificateId = sessionStorage.getItem('pendingCertificateId');

    // If there's a pending certificate and no business has been linked, show confirmation
    if (pendingCertificateId && !hasLinkedBusiness) {
      setShowCloseConfirmation(true);
    } else {
      // Safe to close without confirmation
      handleClose();
    }
  };

  // Actual close handler
  const handleClose = async () => {
    try {
        await handleCertificateRejection();
        setHasLinkedBusiness(false); // Reset for next time
        setShowCloseConfirmation(false);
        onClose();
    }
    catch (error) {
        console.error('Error closing dialog:', error);
    }
  };

  // Handle confirmation dialog
  const handleConfirmClose = async () => {
    await handleClose();
  };

  const handleCancelClose = () => {
    setShowCloseConfirmation(false);
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when search changes
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (!provider) return null;

  return (
    <>
    <Dialog open={isOpen} onOpenChange={handleCloseRequest}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Link Businesses to {provider.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search input */}
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search businesses..."
              className="pl-8"
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          {/* Businesses list */}
          <div className="border rounded-md">
            <ScrollArea className="h-[400px] w-full">
              {isLoading ? (
                <div className="flex items-center justify-center h-full p-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading businesses...</span>
                </div>
              ) : businesses.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full p-4 text-center">
                  <Building2 className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">
                    {debouncedSearchQuery ? 'No businesses found matching your search' : 'No businesses available'}
                  </p>
                </div>
              ) : (
                <div className="space-y-2 p-2">
                  {businesses.map((business) => (
                    <div
                      key={business.businessId}
                      className="flex items-center justify-between p-3 rounded-md border hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={business.photos?.[0]} alt={business.name} />
                          <AvatarFallback>{getBusinessInitials(business.name)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{business.name}</p>
                          <p className="text-sm text-muted-foreground">{business.category}</p>
                        </div>
                      </div>
                      <Button
                        variant={isBusinessLinked(business.businessId) ? "destructive" : "default"}
                        size="sm"
                        onClick={() => handleToggleLink(business)}
                        disabled={linkingBusinessIds.has(business.businessId)}
                      >
                        {linkingBusinessIds.has(business.businessId) ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            {isBusinessLinked(business.businessId) ? 'Unlinking...' : 'Linking...'}
                          </>
                        ) : isBusinessLinked(business.businessId) ? (
                          <>
                            <Link2Off className="h-4 w-4 mr-1" />
                            Unlink
                          </>
                        ) : (
                          <>
                            <Link className="h-4 w-4 mr-1" />
                            Link
                          </>
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center space-x-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1 || isLoading}
              >
                Previous
              </Button>
              <span className="flex items-center px-2">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || isLoading}
              >
                Next
              </Button>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCloseRequest}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Confirmation Dialog for Certificate Rejection */}
    <AlertDialog open={showCloseConfirmation} onOpenChange={setShowCloseConfirmation}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Warning: Certificate Rejection</AlertDialogTitle>
          <AlertDialogDescription>
            Closing this popup will automatically reject the certificate since no businesses have been linked.
            Are you sure you want to continue?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancelClose}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction 
  onClick={handleConfirmClose} 
  className="bg-red-600 hover:bg-red-700"
  disabled={isRejectingCertificate}
>
  {isRejectingCertificate ? (
    <>
      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
      Rejecting...
    </>
  ) : (
    'Yes, Reject Certificate'
  )}
</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
    </>
  );
};

export default BusinessLinkingDialog;
